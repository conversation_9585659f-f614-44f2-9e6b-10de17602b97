/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import { View, Text, FlatList, Pressable, Image, Share } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Clipboard from '@react-native-clipboard/clipboard';
import { useSelector } from 'react-redux';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { showToast } from '@/src/utilities/toast';
import { useQuestionVoting } from '@/src/screens/Forum/components/ForumPost/useHook';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import AiBot from '@/src/assets/images/others/aibot.png';
import Bulb from '@/src/assets/svgs/Bulb';
import Comment from '@/src/assets/svgs/Comment';
import Copy from '@/src/assets/svgs/Copy';
import DownVote from '@/src/assets/svgs/DownVote';
import EditPencil from '@/src/assets/svgs/EditPencil';
import ExcelPreview from '@/src/assets/svgs/ExcelPreview';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import LinkPreview from '@/src/assets/svgs/LinkPreview';
import PathArrow from '@/src/assets/svgs/PathArrow';
import PdfPreview from '@/src/assets/svgs/PdfPreview';
import PhotoPreview from '@/src/assets/svgs/PhotoPreview';
import ReportFlag from '@/src/assets/svgs/ReportFlag';
import ShareIcon from '@/src/assets/svgs/Share';
import SolvedIcon from '@/src/assets/svgs/SolvedIcon';
import TrashBin from '@/src/assets/svgs/TrashBin';
import UpVote from '@/src/assets/svgs/UpVote';
import VideoPreview from '@/src/assets/svgs/VideoPreview';
import Timer from '../Timer';
import FileViewer from '../FileViewer';
import type { ForumPostProps, PreviewIconType } from './types';
const previewIconMap: Record<PreviewIconType, React.FC> = {
  photo: PhotoPreview,
  pdf: PdfPreview,
  video: VideoPreview,
  excel: ExcelPreview,
  link: LinkPreview,
};

const ForumPost: React.FC<{ post: ForumPostProps }> = ({ post }) => {
  const {
    postId,
    canModify,
    profile,
    type,
    topics,
    equipment,
    title,
    isSolved,
    description,
    previewIcons,
    media,
    upVotes,
    downVotes,
    answers,
    comments,
    endTime,
    answerView,
    community = 'General',
    attachments = [],
  } = post;

  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const {
    handleUpvote,
    handleDownvote,
    isUpvoted,
    isDownvoted,
    isLoading: voteLoading,
  } = useQuestionVoting(postId);

  const [optionsVisible, setOptionsVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [fileViewerVisible, setFileViewerVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pendingDeleteAction, setPendingDeleteAction] = useState(false);

  const isOwnPost = Boolean(canModify);

  const handleOptions = () => setOptionsVisible(true);

  const handleCloseOptions = () => setOptionsVisible(false);

  const handleBottomSheetHide = () => {
    if (pendingDeleteAction) {
      setDeleteModalVisible(true);
      setPendingDeleteAction(false);
    }
  };

  const showDeleteConfirmation = () => {
    setPendingDeleteAction(true);
    setOptionsVisible(false);
  };

  const confirmDelete = async () => {
    setIsDeleting(true);
    setIsDeleting(false);
    setDeleteModalVisible(false);
    showToast({
      type: 'success',
      message: 'Deleted',
      description: 'Post deleted successfully',
    });
  };

  const cancelDelete = () => setDeleteModalVisible(false);

  const handleEdit = () => {
    handleCloseOptions();
  };

  const handleReport = () => {
    showToast({
      type: 'success',
      message: 'Reported',
      description: 'Post reported',
    });
    handleCloseOptions();
  };

  const handleCopyLink = () => {
    const postUrl = `https://network.navicater.com/forum/post/${postId}`;
    Clipboard.setString(postUrl);
    showToast({
      type: 'success',
      message: 'Link Copied',
      description: 'Post link copied to clipboard',
    });
    handleCloseOptions();
  };

  const handleShare = async () => {
    try {
      const shareUrl = `https://network.navicater.com/forum/${postId}`;
      const shareMessage = `🚀 Hey there!. just shared an absolutely amazing question on Navicater! 🌟 Don't miss out on this insightful content—check it out now and be inspired! 💡✨ #Navicater #GreatContent`;

      const result = await Share.share({
        message: shareMessage,
        url: shareUrl,
      });

      if (result.action === Share.sharedAction) {
        showToast({
          type: 'success',
          message: 'Shared Successfully!',
          description: 'Your post has been shared 🎉',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Sharing Failed',
        description: 'Oops! Something went wrong. Try again.',
      });
    }
  }

  const chips = [...(equipment || []), ...(topics || [])];

  return (
    <>
      <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg">
        <View className="px-2 flex-row gap-2 items-center">
          {type === 'troubleshooting' && equipment && (
            <View className="bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
              <FlatList
                horizontal
                data={chips}
                keyExtractor={(item) => item.id}

                renderItem={({ item }) => (
                  <View className="flex-row items-center">
                    <Text className="text-[#131313] text-sm font-medium">{item.label}</Text>
                    {item.id !== equipment[equipment.length - 1].id && (
                      <View className="px-2">
                        <PathArrow width={1.25} height={1.25} />
                      </View>
                    )}
                  </View>
                )}
              />
            </View>
          )}
          {type === 'question' && topics && (
            <FlatList
              horizontal
              data={topics}
              keyExtractor={(item) => item.id}
              contentContainerStyle={{
                flexGrow: 1,
              }}
              renderItem={({ item }) => (
                <View className="bg-[#F9F9F9] rounded-lg mr-2 px-2 py-1">
                  <Text className="text-[#131313] text-sm font-medium">{item.label}</Text>
                </View>
              )}
            />
          )}
          {isSolved && (
            <View className="rounded-2xl px-2 py-1 flex-row items-center gap-2">
              <SolvedIcon width={2} height={2} />
            </View>
          )}
        </View>
        <View className="px-3 py-2 flex-row items-center">
          <View style={{ flex: 1 }}>
            <Text
              className="text-[#262626] text-xl font-medium"
              numberOfLines={!answerView ? 1 : undefined}
              ellipsizeMode="tail"
            >
              {title}
            </Text>
          </View>
        </View>
        <View className="py-1 px-3">
          <Text
            className="text-[#262626] text-base font-normal"
            numberOfLines={!answerView ? 1 : undefined}
            ellipsizeMode="tail"
          >
            {description}
          </Text>
        </View>
        {previewIcons && previewIcons.length > 0 && (
          <View className="px-2">
            <FlatList
              horizontal
              data={previewIcons}
              contentContainerStyle={{
                flexGrow: 1,
              }}
              keyExtractor={(item, idx) => item + idx}
              renderItem={({ item }) => {
                const IconComponent = previewIconMap[item];
                return (
                  <Pressable className="p-2" onPress={() => setFileViewerVisible(true)}>
                    <IconComponent />
                  </Pressable>
                );
              }}
            />
          </View>
        )}
        <View className="flex-row justify-between items-center px-4">
          <View className="flex-row items-center gap-5">
            <View className="flex-row items-center gap-2">
              <Pressable onPress={handleUpvote} disabled={voteLoading}>
                <UpVote width={2.5} height={2.5} isLiked={isUpvoted} />
              </Pressable>
              <Text className="text-[#262626] text-sm font-medium">{upVotes}</Text>
            </View>
            <View className="flex-row items-center gap-2">
              <Pressable onPress={handleDownvote} disabled={voteLoading}>
                <DownVote width={2.5} height={2.5} isLiked={isDownvoted} />
              </Pressable>
              <Text className="text-[#262626] text-sm font-medium">{downVotes}</Text>
            </View>
            {!answerView && (
              <>
                <Pressable className="flex-row items-center gap-2">
                  <Bulb width={2.5} height={2.5} />
                  <Text className="text-[#262626] text-sm font-medium">{answers}</Text>
                </Pressable>
                <Pressable
                  className="flex-row items-center gap-2"
                  onPress={() => navigation.navigate('ForumComments', { questionId: postId })}
                >
                  <Comment width={2.5} height={2.5} color="#525252" />
                  <Text className="text-[#262626] text-sm font-medium">{comments || 0}</Text>
                </Pressable>
              </>
            )}
            {answerView && (
              <Pressable
                className="flex-row items-center gap-2"
                onPress={() => navigation.navigate('ForumComments', { questionId: postId })}
              >
                <Comment width={2.5} height={2.5} color="#525252" />
                <Text className="text-[#262626] text-sm font-medium">{comments || 0}</Text>
              </Pressable>
            )}
          </View>
          <View className="flex-row items-center gap-3">
            <Pressable>
              <Timer endTime={endTime} />
            </Pressable>
            <Pressable onPress={() => handleShare()}>
              <ShareIcon width={2.5} height={2.5} />
            </Pressable>
          </View>
        </View>
        {answerView && (
          <View className="flex-row items-center justify-between px-4 py-3">
            <View className="flex-row items-center gap-2">
              <Pressable>
                <UserAvatar
                  avatarUri={profile.avatar}
                  width={28}
                  height={28}
                />
              </Pressable>
              <Text className="text-[#262626] text-base font-normal">
                {profile.name} in <Text className="font-medium italic">{community}</Text>
              </Text>
            </View>
            <Pressable onPress={handleOptions}>
              <HorizontalEllipsis width={2.5} height={2.5} />
            </Pressable>
          </View>
        )}
      </View>
      <BottomSheet
        height={isOwnPost ? 230 : 200}
        visible={optionsVisible}
        onClose={handleCloseOptions}
        onModalHide={handleBottomSheetHide}
      >
        <OptionsMenu>
          {isOwnPost ? (
            <>
              <OptionItem
                icon={<TrashBin stroke="#EF4444" strokeWidth={1.5} width={2} height={2} />}
                label="Delete post"
                textClassName="text-red-500"
                onPress={showDeleteConfirmation}
              />
            </>
          ) : (
            <OptionItem
              icon={<ReportFlag stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Report post"
              onPress={handleReport}
            />
          )}
          <View className="h-[1px] bg-gray-200 my-2" />
          <OptionItem
            icon={<Copy stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
            label="Copy Link"
            onPress={handleCopyLink}
          />
          <View className="h-[1px] bg-gray-200 my-2" />
          {isOwnPost && (
            <OptionItem
              icon={<EditPencil stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Edit post"
              onPress={handleEdit}
            />
          )}
        </OptionsMenu>
      </BottomSheet>
      <CustomModal
        isVisible={deleteModalVisible}
        title="Delete Post"
        description="Are you sure you want to delete this post? This action cannot be undone."
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        isConfirming={isDeleting}
      />

      {fileViewerVisible && attachments.length > 0 && (
        <FileViewer
          isVisible={fileViewerVisible}
          onClose={() => setFileViewerVisible(false)}
          attachments={attachments}
        />
      )}
    </>
  );
};

export default ForumPost;
