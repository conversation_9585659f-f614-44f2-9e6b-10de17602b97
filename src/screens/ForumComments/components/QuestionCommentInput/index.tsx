/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useRef, useState, useEffect } from 'react';
import {
  Keyboard,
  TextInput,
  View,
  Pressable,
  Text,
  ActivityIndicator,
  Platform,
} from 'react-native';
import UserAvatar from '@/src/components/UserAvatar';
import Send from '@/src/assets/svgs/Send';
import Close from '@/src/assets/svgs/Close';
import type { CommentInputProps } from '../../CommentItem/types';

const MAX_COMMENT_LENGTH = 255;

const QuestionCommentInput: React.FC<CommentInputProps> = ({
  questionId,
  parentCommentId,
  replyPreview,
  onSubmit,
  onClearReply,
  isSubmitting = false,
}) => {
  const [comment, setComment] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const isReply = !!parentCommentId;
  const canSubmit = comment.trim().length > 0 && !isSubmitting;

  useEffect(() => {
    if (replyPreview && inputRef.current) {
      inputRef.current.focus();
    }
  }, [replyPreview]);

  const handleCommentText = (text: string) => {
    if (text.length <= MAX_COMMENT_LENGTH) {
      setComment(text);
    }
  };

  const handleSend = () => {
    if (canSubmit) {
      onSubmit(comment.trim());
      setComment('');
      Keyboard.dismiss();
    }
  };

  const handleClearReply = () => {
    if (onClearReply) {
      onClearReply();
    }
    Keyboard.dismiss();
  };

  return (
    <View className="bg-white border-t border-gray-200">
      {/* Reply Preview */}
      {replyPreview && (
        <View className="px-4 py-3 bg-gray-50 border-b border-gray-200">
          <View className="flex-row items-center justify-between">
            <View className="flex-1">
              <Text className="text-sm text-gray-600">
                Replying to <Text className="font-medium">{replyPreview.userName}</Text>
              </Text>
              <Text className="text-sm text-gray-500 mt-1" numberOfLines={2}>
                {replyPreview.text}
              </Text>
            </View>
            <Pressable onPress={handleClearReply} className="p-2">
              <Close width={16} height={16} fill="#6B7280" />
            </Pressable>
          </View>
        </View>
      )}

      {/* Input Section */}
      <View className="flex-row items-end px-4 py-3">
        <UserAvatar
          avatar={null} // Will be filled with current user avatar
          name="You"
          size={36}
        />

        <View className="flex-1 ml-3">
          <View className="relative">
            <TextInput
              ref={inputRef}
              className="bg-gray-100 rounded-2xl text-black px-4 py-3 pr-12 text-base min-h-[44px] max-h-[120px]"
              placeholder={isReply ? `Reply to ${replyPreview?.userName || 'comment'}...` : 'Write a comment...'}
              placeholderTextColor="#9CA3AF"
              value={comment}
              onChangeText={handleCommentText}
              maxLength={MAX_COMMENT_LENGTH}
              multiline
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              autoCorrect
              editable={!isSubmitting}
              textAlignVertical="top"
            />
            <Pressable
              onPress={handleSend}
              disabled={!canSubmit}
              className={`absolute right-2 bottom-2 w-8 h-8 rounded-full items-center justify-center ${
                canSubmit ? 'bg-green-800' : 'bg-gray-300'
              }`}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Send width={16} height={16} fill="white" />
              )}
            </Pressable>
          </View>

          {/* Character Count */}
          {(isFocused || comment.length > 200) && (
            <Text className={`text-xs mt-1 text-right ${
              comment.length > 240 ? 'text-red-500' : 'text-gray-500'
            }`}>
              {comment.length}/{MAX_COMMENT_LENGTH}
            </Text>
          )}
        </View>
      </View>
    </View>
  );
};

export default QuestionCommentInput;
