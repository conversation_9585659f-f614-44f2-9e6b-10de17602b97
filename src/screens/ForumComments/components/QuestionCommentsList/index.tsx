/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, FlatList, Text, Pressable, ActivityIndicator } from 'react-native';
import QuestionCommentItem from '../QuestionCommentItem';
import type { QuestionCommentI } from '@/src/networks/forum/types';

type QuestionCommentsListProps = {
  comments: QuestionCommentI[];
  loading: boolean;
  hasMore: boolean;
  onReply: (comment: QuestionCommentI) => void;
  onDelete?: (commentId: string) => void;
  onShowMoreReplies?: (commentId: string) => void;
  onLoadMore?: () => void;
  canDelete?: (comment: QuestionCommentI) => boolean;
  emptyMessage?: string;
};

const QuestionCommentsList: React.FC<QuestionCommentsListProps> = ({
  comments,
  loading,
  hasMore,
  onReply,
  onDelete,
  onShowMoreReplies,
  onLoadMore,
  canDelete,
  emptyMessage = 'No comments yet. Be the first to comment!',
}) => {
  const renderComment = ({ item }: { item: QuestionCommentI }) => (
    <QuestionCommentItem
      comment={item}
      onReply={onReply}
      onDelete={onDelete}
      onShowMoreReplies={onShowMoreReplies}
      canDelete={canDelete ? canDelete(item) : false}
    />
  );

  const renderFooter = () => {
    if (!hasMore) return null;
    
    return (
      <View className="py-4">
        {loading ? (
          <View className="flex-row items-center justify-center">
            <ActivityIndicator size="small" color="#059669" />
            <Text className="ml-2 text-gray-600">Loading more comments...</Text>
          </View>
        ) : (
          <Pressable onPress={onLoadMore} className="items-center py-2">
            <Text className="text-blue-600 font-medium">Show more comments</Text>
          </Pressable>
        )}
      </View>
    );
  };

  const renderEmpty = () => (
    <View className="flex-1 items-center justify-center py-12">
      <Text className="text-gray-500 text-center">{emptyMessage}</Text>
    </View>
  );

  if (loading && comments.length === 0) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" color="#059669" />
        <Text className="mt-2 text-gray-600">Loading comments...</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={comments}
      keyExtractor={(item) => item.id}
      renderItem={renderComment}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        flexGrow: 1,
        backgroundColor: 'white',
      }}
      onEndReached={hasMore && !loading ? onLoadMore : undefined}
      onEndReachedThreshold={0.1}
    />
  );
};

export default QuestionCommentsList;
