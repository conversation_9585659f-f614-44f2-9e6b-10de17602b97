/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, Text, Pressable } from 'react-native';
import UserAvatar from '@/src/components/UserAvatar';
import { formatSocialTime } from '@/src/utilities/datetime';
import TrashBin from '@/src/assets/svgs/TrashBin';
import type { QuestionCommentItemProps } from '../../CommentItem/types';

const QuestionCommentItem: React.FC<QuestionCommentItemProps> = ({
  comment,
  onReply,
  onDelete,
  onShowMoreReplies,
  canDelete = false,
  showRepliesCount = true,
}) => {
  const handleReply = () => {
    onReply(comment);
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(comment.id);
    }
  };

  const handleShowMoreReplies = () => {
    if (onShowMoreReplies) {
      onShowMoreReplies(comment.id);
    }
  };

  const timeAgo = formatSocialTime(comment.createdAt);

  return (
    <View className="bg-white p-4 border-b border-gray-100">
      {/* Main Comment */}
      <View className="flex-row">
        <UserAvatar
          avatarUri={comment.profile?.avatar}
          name={comment.profile?.name || 'Anonymous'}
          width={40}
          height={40}
        />
        <View className="flex-1 ml-3">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Text className="font-medium text-gray-900">
                {comment.isAnonymous ? 'Anonymous' : comment.profile?.name}
              </Text>
              <Text className="text-gray-500 text-sm ml-2">{timeAgo}</Text>
            </View>
            {canDelete && (
              <Pressable onPress={handleDelete} className="p-1">
                <TrashBin width={1.5} height={1.5} stroke="#EF4444" />
              </Pressable>
            )}
          </View>
          
          <Text className="text-gray-800 mt-1 leading-5">{comment.text}</Text>
          
          <View className="flex-row items-center mt-2">
            <Pressable onPress={handleReply} className="mr-4">
              <Text className="text-blue-600 text-sm font-medium">Reply</Text>
            </Pressable>
            
            {showRepliesCount && comment.replyCount > 0 && (
              <Text className="text-gray-500 text-sm">
                {comment.replyCount} {comment.replyCount === 1 ? 'reply' : 'replies'}
              </Text>
            )}
          </View>
        </View>
      </View>

      {/* Replies Preview */}
      {comment.replies && comment.replies.length > 0 && (
        <View className="ml-12 mt-3">
          {comment.replies.map((reply) => (
            <View key={reply.id} className="flex-row mb-3">
              <UserAvatar
                avatarUri={reply.profile?.avatar}
                name={reply.profile?.name || 'Anonymous'}
                width={32}
                height={32}
              />
              <View className="flex-1 ml-2">
                <View className="flex-row items-center">
                  <Text className="font-medium text-gray-900 text-sm">
                    {reply.isAnonymous ? 'Anonymous' : reply.profile?.name}
                  </Text>
                  <Text className="text-gray-500 text-xs ml-2">
                    {formatSocialTime(reply.createdAt)}
                  </Text>
                </View>
                <Text className="text-gray-800 text-sm mt-1 leading-4">{reply.text}</Text>
              </View>
            </View>
          ))}
          
          {comment.replyCount > (comment.replies?.length || 0) && (
            <Pressable onPress={handleShowMoreReplies} className="mt-2">
              <Text className="text-blue-600 text-sm font-medium">
                Show {comment.replyCount - (comment.replies?.length || 0)} more replies
              </Text>
            </Pressable>
          )}
        </View>
      )}
    </View>
  );
};

export default QuestionCommentItem;
