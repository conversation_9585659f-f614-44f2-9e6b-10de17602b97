/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type { QuestionCommentI, QuestionCommentReplyI } from '@/src/networks/forum/types';

export type ForumCommentReplyProps = {
  replyId: string;
  commentId: string;
  userId: string;
  content: string;
};

export type ForumCommentProps = {
  commentId: string;
  commentsType: 'postComment' | 'answersComment';
  postId?: string;
  answerId?: string;
  userId: string;
  content: string;
  replies: ForumCommentReplyProps[];
};

// New types for question comments
export type QuestionCommentItemProps = {
  comment: QuestionCommentI;
  onReply: (comment: QuestionCommentI) => void;
  onDelete?: (commentId: string) => void;
  onShowMoreReplies?: (commentId: string) => void;
  canDelete?: boolean;
  showRepliesCount?: boolean;
};

export type QuestionCommentReplyItemProps = {
  reply: QuestionCommentReplyI;
  onDelete?: (replyId: string) => void;
  canDelete?: boolean;
};

export type CommentInputProps = {
  questionId: string;
  parentCommentId?: string;
  replyPreview?: {
    commentId: string;
    text: string;
    userName: string;
  } | null;
  onSubmit: (text: string) => void;
  onClearReply?: () => void;
  isSubmitting?: boolean;
};
