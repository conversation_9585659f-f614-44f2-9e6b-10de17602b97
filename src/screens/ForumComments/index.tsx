/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState, useEffect } from 'react';
import { View, Text, Alert } from 'react-native';
import { KeyboardAvoidingView, Platform, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { RootState } from '@/src/redux/store';
import QuestionCommentsList from './components/QuestionCommentsList';
import QuestionCommentInput from './components/QuestionCommentInput';
import { useQuestionComments } from './hooks/useQuestionComments';
import type { QuestionCommentI } from '@/src/networks/forum/types';
// Legacy imports for answer comments (to be implemented later)
import CommentInput from './CommentInput';
import type { ForumCommentProps } from './CommentItem/types';
import CommentsList from './CommentsList';
import { useAnswers } from '../ForumAnswers/AnswersList/useHook';

const ForumCommentsScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const route = useRoute();
  const { questionId, answerId } = route.params as { questionId?: string; answerId?: string };

  // Get current user info
  const currentUser = useSelector((state: RootState) => ({
    id: state.user.profileId,
    name: state.user.fullName,
    avatar: state.user.avatar,
  }));

  // State for reply functionality
  const [replyPreview, setReplyPreview] = useState<{
    commentId: string;
    text: string;
    userName: string;
  } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Question comments hook
  const {
    comments,
    loading,
    error,
    hasMore,
    fetchComments,
    loadMoreComments,
    fetchReplies,
    createComment,
    deleteComment,
    canDeleteComment,
  } = useQuestionComments(currentUser?.id);

  // Legacy answer comments (for backward compatibility)
  const { answerDetail, loading: answerLoading, error: answerError, getAnswerDetail } = useAnswers();
  const [legacyComments, setLegacyComments] = useState<ForumCommentProps[]>([]);

  // Determine which type of comments to show
  const isQuestionComments = !!questionId;
  const isAnswerComments = !!answerId;

  useEffect(() => {
    if (questionId) {
      fetchComments(questionId, true);
    } else if (answerId) {
      getAnswerDetail(answerId);
    }
  }, [questionId, answerId, fetchComments, getAnswerDetail]);


  // Handle comment submission
  const handleSubmit = async (text: string) => {
    if (!questionId) return;

    setIsSubmitting(true);
    try {
      await createComment(
        questionId,
        text,
        replyPreview?.commentId
      );
      setReplyPreview(null);
    } catch (error) {
      Alert.alert('Error', 'Failed to post comment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle reply to comment
  const handleReply = (comment: QuestionCommentI) => {
    setReplyPreview({
      commentId: comment.id,
      text: comment.text,
      userName: comment.isAnonymous ? 'Anonymous' : comment.profile?.name || 'Unknown',
    });
  };

  // Handle delete comment
  const handleDelete = async (commentId: string) => {
    Alert.alert(
      'Delete Comment',
      'Are you sure you want to delete this comment?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteComment(commentId);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete comment. Please try again.');
            }
          },
        },
      ]
    );
  };

  // Handle show more replies
  const handleShowMoreReplies = (commentId: string) => {
    if (questionId) {
      fetchReplies(questionId, commentId);
    }
  };

  // Handle load more comments
  const handleLoadMore = () => {
    if (questionId) {
      loadMoreComments(questionId);
    }
  };

  // Clear reply preview
  const handleClearReply = () => {
    setReplyPreview(null);
  };
  // Render question comments
  if (isQuestionComments) {
    return (
      <SafeArea>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <KeyboardAvoidingView
            className="flex-1"
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
          >
            <View className="flex-row items-center px-4 py-3 bg-white border-b border-gray-200">
              <BackButton onBack={() => navigation.goBack()} label="" />
              <Text className="text-xl font-medium ml-2">Comments</Text>
            </View>

            <QuestionCommentsList
              comments={comments}
              loading={loading}
              hasMore={hasMore}
              onReply={handleReply}
              onDelete={handleDelete}
              onShowMoreReplies={handleShowMoreReplies}
              onLoadMore={handleLoadMore}
              canDelete={canDeleteComment}
            />

            <QuestionCommentInput
              questionId={questionId}
              parentCommentId={replyPreview?.commentId}
              replyPreview={replyPreview}
              onSubmit={handleSubmit}
              onClearReply={handleClearReply}
              isSubmitting={isSubmitting}
            />
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </SafeArea>
    );
  }

  // Render legacy answer comments (for backward compatibility)
  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <View className="flex-row items-center px-4">
            <BackButton onBack={() => navigation.goBack()} label="" />
            <Text className="text-xl font-medium">Comments</Text>
          </View>
          <CommentInput
            userId="currentUser"
            postId="1"
            parentCommentId={replyPreview?.commentId}
            replyPreview={replyPreview ? {
              commentId: replyPreview.commentId,
              content: replyPreview.text,
              userId: replyPreview.userName,
            } : null}
            setReplyPreview={(preview) => {
              if (preview && typeof preview === 'object' && 'commentId' in preview) {
                setReplyPreview({
                  commentId: preview.commentId,
                  text: preview.content,
                  userName: preview.userId,
                });
              } else {
                setReplyPreview(null);
              }
            }}
            type="postComment"
            onSubmit={async (text: string) => {
              // Legacy implementation for answer comments
              console.log('Legacy comment submission:', text);
            }}
          />
          <CommentsList
            comments={legacyComments}
            onReply={(comment) =>
              setReplyPreview({
                commentId: comment.commentId,
                text: comment.content,
                userName: comment.userId,
              })
            }
          />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default ForumCommentsScreen;
