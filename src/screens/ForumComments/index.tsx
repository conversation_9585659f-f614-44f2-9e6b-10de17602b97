/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import { View, Text } from 'react-native';
import { KeyboardAvoidingView, Platform, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CommentInput from './CommentInput';
import type { ForumCommentProps } from './CommentItem/types';
import CommentsList from './CommentsList';
import { useAnswers } from '../ForumAnswers/AnswersList/useHook';

const initialComments: ForumCommentProps[] = [
  {
    commentId: '1',
    userId: 'user1',
    commentsType: 'postComment',
    content: 'This is a sample comment to demonstrate the comments list.',
    replies: [
      { replyId: '1-1', commentId: '1', userId: 'user2', content: 'Thank you!' },
      { replyId: '1-2', commentId: '1', userId: 'user3', content: 'Agreed!' },
    ],
  },
  {
    commentId: '2',
    userId: 'user4',
    commentsType: 'postComment',
    content: 'Can you explain more?',
    replies: [],
  },
];

const ForumCommentsScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const [comments, setComments] = useState<ForumCommentProps[]>(initialComments);
  const [replyPreview, setReplyPreview] = useState<{
    commentId: string;
    content: string;
    userId: string;
  } | null>(null);

  const route = useRoute();
  const { answerId } = route.params as { answerId: string };
  const { answerDetail, loading, error, getAnswerDetail } = useAnswers();

  React.useEffect(() => {
    if (answerId) {
      getAnswerDetail(answerId);
    }
  }, [answerId, getAnswerDetail]);


  const handleSubmit = (text: string) => {
    if (replyPreview) {
      setComments((prev) =>
        prev.map((comment) =>
          comment.commentId === replyPreview.commentId
            ? {
              ...comment,
              replies: [
                ...comment.replies,
                {
                  replyId: '5',
                  commentId: comment.commentId,
                  userId: 'currentUser',
                  content: text,
                },
              ],
            }
            : comment,
        ),
      );
      setReplyPreview(null);
    } else {
      setComments((prev) => [
        ...prev,
        {
          commentId: '5',
          userId: 'currentUser',
          commentsType: 'postComment',
          content: text,
          replies: [],
        },
      ]);
    }
  };
  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <View className="flex-row items-center px-4">
            <BackButton onBack={() => navigation.goBack()} label="" />
            <Text className="text-xl font-medium">Comments</Text>
          </View>
          <CommentsList
            comments={comments}
            onReply={(comment) =>
              setReplyPreview({
                commentId: comment.commentId,
                content: comment.content,
                userId: comment.userId,
              })
            }
          />
          <CommentInput
            userId="currentUser"
            postId="1"
            parentCommentId={replyPreview?.commentId}
            replyPreview={replyPreview}
            setReplyPreview={setReplyPreview}
            type="postComment"
            onSubmit={handleSubmit}
          />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default ForumCommentsScreen;
