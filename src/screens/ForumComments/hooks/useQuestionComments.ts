/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/src/redux/store';
import {
  fetchQuestionComments,
  fetchQuestionCommentReplies,
  createQuestionComment,
  deleteQuestionComment,
} from '@/src/redux/slices/forum/forumSlice';
import type { AppDispatch } from '@/src/redux/store';
import type { QuestionCommentI } from '@/src/networks/forum/types';

type UseQuestionCommentsResult = {
  comments: QuestionCommentI[];
  loading: boolean;
  error: string | null;
  total: number;
  hasMore: boolean;
  commentReplies: Record<string, {
    replies: any[];
    total: number;
    nextCursorId: number | null;
  }>;
  commentRepliesLoading: Record<string, boolean>;
  fetchComments: (questionId: string, refresh?: boolean) => void;
  loadMoreComments: (questionId: string) => void;
  fetchReplies: (questionId: string, parentCommentId: string, refresh?: boolean) => void;
  loadMoreReplies: (questionId: string, parentCommentId: string) => void;
  createComment: (questionId: string, text: string, parentCommentId?: string) => Promise<void>;
  deleteComment: (commentId: string) => Promise<void>;
  canDeleteComment: (comment: QuestionCommentI) => boolean;
};

export const useQuestionComments = (currentUserId?: string): UseQuestionCommentsResult => {
  const dispatch = useDispatch<AppDispatch>();
  
  const {
    questionComments,
    questionCommentsLoading,
    questionCommentsError,
    questionCommentsTotal,
    questionCommentsNextCursorId,
    commentReplies,
    commentRepliesLoading,
    commentRepliesError,
  } = useSelector((state: RootState) => state.forum);

  const hasMore = questionCommentsNextCursorId !== null;

  const fetchComments = useCallback((questionId: string, refresh = false) => {
    const cursorId = refresh ? null : questionCommentsNextCursorId;
    dispatch(fetchQuestionComments({
      questionId,
      cursorId,
      pageSize: 10,
    }));
  }, [dispatch, questionCommentsNextCursorId]);

  const loadMoreComments = useCallback((questionId: string) => {
    if (hasMore && !questionCommentsLoading) {
      fetchComments(questionId, false);
    }
  }, [fetchComments, hasMore, questionCommentsLoading]);

  const fetchReplies = useCallback((questionId: string, parentCommentId: string, refresh = false) => {
    const existingReplies = commentReplies[parentCommentId];
    const cursorId = refresh ? null : existingReplies?.nextCursorId;
    
    dispatch(fetchQuestionCommentReplies({
      questionId,
      parentCommentId,
      cursorId,
      pageSize: 10,
    }));
  }, [dispatch, commentReplies]);

  const loadMoreReplies = useCallback((questionId: string, parentCommentId: string) => {
    const existingReplies = commentReplies[parentCommentId];
    const isLoading = commentRepliesLoading[parentCommentId];
    const hasMoreReplies = existingReplies?.nextCursorId !== null;
    
    if (hasMoreReplies && !isLoading) {
      fetchReplies(questionId, parentCommentId, false);
    }
  }, [fetchReplies, commentReplies, commentRepliesLoading]);

  const createComment = useCallback(async (questionId: string, text: string, parentCommentId?: string) => {
    try {
      await dispatch(createQuestionComment({
        questionId,
        text,
        parentCommentId,
      })).unwrap();
      
      // Refresh comments after creating
      fetchComments(questionId, true);
    } catch (error) {
      throw error;
    }
  }, [dispatch, fetchComments]);

  const deleteComment = useCallback(async (commentId: string) => {
    try {
      await dispatch(deleteQuestionComment({ commentId })).unwrap();
    } catch (error) {
      throw error;
    }
  }, [dispatch]);

  const canDeleteComment = useCallback((comment: QuestionCommentI) => {
    if (!currentUserId) return false;
    
    // User can delete their own comments
    if (comment.profile?.id === currentUserId) return true;
    
    // TODO: Add logic for moderators/admins
    return false;
  }, [currentUserId]);

  return {
    comments: questionComments,
    loading: questionCommentsLoading,
    error: questionCommentsError,
    total: questionCommentsTotal,
    hasMore,
    commentReplies,
    commentRepliesLoading,
    fetchComments,
    loadMoreComments,
    fetchReplies,
    loadMoreReplies,
    createComment,
    deleteComment,
    canDeleteComment,
  };
};
