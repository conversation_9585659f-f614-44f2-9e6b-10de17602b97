import { Text, View } from 'react-native';
import Button from '@/src/components/Button';
import { CertificateI } from '../../../../types';
import { ExpandCertificationPropsI } from './types';

export const CertificationExpand = ({
  certification,
}: {
  certification: ExpandCertificationPropsI;
}) => {
  const onPress = () => {
    return;
  };

  const formatDate = (date: Date) =>
    new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric',
    });

  return (
    <View>
      <View className="flex-row items-center">
        <Text className="text-[#525252]">{`Certified on : ${formatDate(new Date(certification.fromDate))}`}</Text>
        <View className="w-px h-4 bg-[#525252] mx-2" />
        <Text className="text-[#525252]">{`Validity : ${certification.untilDate ? formatDate(new Date(certification.untilDate)) : "Unlimited Validity"}`}</Text>
      </View>
    </View>
  );
};
