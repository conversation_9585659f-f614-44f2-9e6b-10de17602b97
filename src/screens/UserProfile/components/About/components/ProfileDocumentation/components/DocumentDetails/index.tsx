import { Text, View } from 'react-native';
import WarningBadgeIcon from '@/src/assets/svgs/WarningBadge';
import { IdentityDocumentDetailsTypeI, VisaDocumentDetailsTypeI } from './types';

export const IdentityDocumentDetails = ({
  document,
  isLast,
  expiresIn,
}: {
  document: IdentityDocumentDetailsTypeI;
  isLast: boolean;
  expiresIn: string | (string | number)[];
}) => {
  const isExpired = expiresIn.length === 0;
  const isUnlimited = expiresIn.length > 0 && expiresIn[0] === 'Unlimited';
  const [timeLeft, unit] = expiresIn;

  return (
    <View className={`${isLast ? '' : 'border-b border-[#D4D4D4]'} py-8`}>
      <Text className={`text-base font-medium ${isExpired ? 'text-gray-400' : 'text-black'}`}>
        {document.type}
      </Text>
      <View className="flex-row items-center">
        <Text className={`text-sm ${isExpired ? 'text-gray-400' : 'text-black'}`}>
          {document.countryName}
        </Text>
        <View className={`w-px h-4 mx-2 ${isExpired ? 'bg-gray-400' : 'bg-black'}`} />
        {expiresIn === 'unlimited' ? (
          <Text className="text-sm text-black">Unlimited Validity</Text>
        ) : (
          <>
            <Text className={`text-sm ${isExpired ? 'text-gray-400' : 'text-black'}`}>
              {isExpired
                ? 'Expired'
                : isUnlimited
                ? 'Unlimited validity'
                : unit === 'D'
                ? `Expires in ${timeLeft} days`
                : (timeLeft as number) > 6
                ? `Valid for ${timeLeft} months`
                : `Expires in ${timeLeft} months`}
            </Text>
            {!isExpired && !isUnlimited && (timeLeft as number) < 6 ? (
              <View className="p-1">
                <WarningBadgeIcon />
              </View>
            ) : null}
          </>
        )}
      </View>
    </View>
  );
};

export const VisaDocumentDetails = ({
  document,
  isLast,
  expiresIn,
}: {
  document: VisaDocumentDetailsTypeI;
  isLast: boolean;
  expiresIn: string | (string | number)[];
}) => {
  const isExpired = expiresIn.length === 0;
  const [timeLeft, unit] = expiresIn;

  return (
    <View className={`${isLast ? '' : 'border-b border-[#D4D4D4]'} pb-4 pt-4`}>
      <Text className={`text-base font-medium ${isExpired ? 'text-gray-400' : 'text-black'}`}>
        {document.country.name}
      </Text>
      {expiresIn === 'unlimited' ? (
        <Text className="text-sm text-black">Unlimited Validity</Text>
      ) : (
        <Text className={`text-sm ${isExpired ? 'text-gray-400' : 'text-black'}`}>
          {isExpired
            ? 'Expired'
            : unit === 'D'
            ? `Expires in ${timeLeft} days`
            : (timeLeft as number) > 6
            ? `Valid for ${timeLeft} months`
            : `Expires in ${timeLeft} months`}
        </Text>
      )}
    </View>
  );
};
