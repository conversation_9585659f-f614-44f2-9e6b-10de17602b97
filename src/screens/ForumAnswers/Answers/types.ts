/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ProfileForDataI } from '@/src/networks/question/types';

export type ForumAnswerProps = {
  answerId: string;
  postId: string;
  profile: ProfileForDataI;
  content: string;
  answerVerified: boolean;
  upVotes: number;
  downVotes: number;
  comments: number;
  commentView: boolean;
  userVote?: 'UPVOTE' | 'DOWNVOTE' | null; // Current user's vote on this answer
};


