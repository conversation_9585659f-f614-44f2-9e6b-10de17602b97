/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import {
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ActivityIndicator,
  Text,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import AnswerInput from './AnswerInput';
import AnswersList from './AnswersList';
import { useSelector } from 'react-redux';
import { selectForumQuestion, selectForumLoading, selectForumError } from '@/src/redux/selectors/forum';
import { mapForumAnswers } from './utils';
import NotFound from '@/src/components/NotFound';
import { useAnswers } from './AnswersList/useHook';
import type { ForumAnswerWithProfileForQuestionI } from '@/src/networks/question/types';
import AnswerListSkeleton from './AnswerListSkeleton';
import { selectCurrentUser } from '@/src/redux/selectors/user';


const ForumAnswersScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const question = useSelector(selectForumQuestion);
  const loading = useSelector(selectForumLoading);
  const error = useSelector(selectForumError);

  const [previewAnswers, setPreviewAnswers] = useState<ForumAnswerWithProfileForQuestionI[]>([]);

  const { createForumAnswer } = useAnswers();

  const { answers, loading: answersLoading, error: answersError, getAnswersForQuestion } = useAnswers();

  // const mappedAnswers = mapForumAnswers(answers, question?.id ?? '');

  useEffect(() => {
    if (question?.id) {
      getAnswersForQuestion(question.id, null, 10);
    }
  }, [question?.id, getAnswersForQuestion]);

  const currentUser = useSelector(selectCurrentUser);

  const handleSubmit = (text: string) => {
    if (!question?.id) return;
    const tempId = `temp_${Date.now()}`;
    const previewAnswer: ForumAnswerWithProfileForQuestionI = {
      id: tempId,
      cursorId: 0,
      text,
      upvoteCount: 0,
      downvoteCount: 0,
      commentCount: 0,
      status: '',
      isEdited: false,
      createdAt: new Date().toISOString(),
      profile: {
        id: currentUser?.profileId || 'temp',
        name: currentUser?.fullName || 'You',
        avatar: currentUser?.avatar || null,
      },
    };
    setPreviewAnswers((prev) => [previewAnswer, ...prev]);
    createForumAnswer({ text, questionId: question.id });
  };

  const mappedAnswers = mapForumAnswers([...previewAnswers, ...(answers || [])], question?.id ?? '');

const isInitialLoading = (loading || answersLoading) && (!answers || answers.length === 0);

if (isInitialLoading) {
  return (
    <SafeArea>
      <AnswerListSkeleton />
    </SafeArea>
  );
}
  if (isInitialLoading) {
    return (
      <SafeArea>
        <AnswerListSkeleton />
      </SafeArea>
    );
  }


  console.log('question', question, 'answers', answers);

  if (!question) {
    return (
      <SafeArea>
        <NotFound
          title="No Question Found"
        />
      </SafeArea>
    )
  }

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <View className="flex-row items-center px-4">
            <BackButton onBack={() => navigation.goBack()} label="" />
          </View>
          <AnswerInput onSubmit={handleSubmit} />

          <AnswersList answers={mappedAnswers} question={question} />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default ForumAnswersScreen;
