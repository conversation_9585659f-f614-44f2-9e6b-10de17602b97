import type { ForumAnswerWithProfileForQuestionI } from '@/src/networks/question/types';
import type { ForumAnswerWithProfileI } from '@/src/networks/answer/types';
import { ForumAnswerProps } from '../Answers/types';

type AnswerInput = (ForumAnswerWithProfileForQuestionI | ForumAnswerWithProfileI);

export function mapForumAnswers(
  answers: AnswerInput[] | null,
  postId: string
): ForumAnswerProps[] {
  if (!answers) return [];
  return answers.map((a) => ({
    answerId: a.id,
    postId,
    profile: a.profile,
    content: a.text,
    answerVerified: (a as ForumAnswerWithProfileForQuestionI).status
      ? (a as ForumAnswerWithProfileForQuestionI).status === 'VERIFIED'
      : false,
    upVotes: a.upvoteCount,
    downVotes: a.downvoteCount,
    comments: a.commentCount,
    commentView: false,
  }));
}