import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectForumAnswers,
  selectForumAnswersTotal,
  selectForumAnswersNextCursorId,
  selectForumAnswerLoading,
  selectForumAnswerError,
  selectForumAnswerDetail,
  selectAnswerVotes,
  selectAnswerVotesTotal,
  selectAnswerVotesNextCursorId,
  selectAnswerVoteLoading,
  selectAnswerVoteError,
} from '@/src/redux/selectors/forum';
import {
  fetchAnswersForQuestion,
  fetchAnswerDetail,
  createAnswer,
  fetchAnswerVotes,
  createAnswerVote,
} from '@/src/redux/slices/forum/forumSlice';
import { AppDispatch } from '@/src/redux/store';
import type { ForumAnswerCreateOnePayloadI } from '@/src/networks/answer/types';
import type {
  ForumAnswerVoteFetchManyParamsI,
  ForumAnswerVoteCreateOnePayloadI,
} from '@/src/networks/answerVote/types';

export const useAnswers = () => {
  const dispatch = useDispatch<AppDispatch>();

  const answers = useSelector(selectForumAnswers);
  const total = useSelector(selectForumAnswersTotal);
  const nextCursorId = useSelector(selectForumAnswersNextCursorId);
  const loading = useSelector(selectForumAnswerLoading);
  const error = useSelector(selectForumAnswerError);
  const answerDetail = useSelector(selectForumAnswerDetail);

  const getAnswersForQuestion = useCallback(
    (questionId: string, cursorId?: number | null, pageSize: number = 10) => {
      dispatch(fetchAnswersForQuestion({ questionId, cursorId, pageSize }));
    },
    [dispatch],
  );

    const handleLoadMore = useCallback(
    (questionId: string) => {
      if (!loading && nextCursorId) {
        dispatch(fetchAnswersForQuestion({ questionId, cursorId: nextCursorId, pageSize: 10 }));
      }
    },
    [dispatch, loading, nextCursorId]
  );

  const getAnswerDetail = useCallback(
    (answerId: string) => {
      dispatch(fetchAnswerDetail(answerId));
    },
    [dispatch],
  );

  const createForumAnswer = useCallback(
    (payload: ForumAnswerCreateOnePayloadI) => {
      dispatch(createAnswer(payload));
    },
    [dispatch],
  );

  const getVotes = useCallback(
    (params: ForumAnswerVoteFetchManyParamsI) => {
      dispatch(fetchAnswerVotes(params));
    },
    [dispatch],
  );

  const vote = useCallback(
    (payload: ForumAnswerVoteCreateOnePayloadI) => {
      dispatch(createAnswerVote(payload));
    },
    [dispatch],
  );

  return {
    answers,
    total,
    nextCursorId,
    loading,
    error,
    answerDetail,
    getAnswersForQuestion,
    handleLoadMore,
    getAnswerDetail,
    createForumAnswer,
    getVotes,
    vote,
  };
};
