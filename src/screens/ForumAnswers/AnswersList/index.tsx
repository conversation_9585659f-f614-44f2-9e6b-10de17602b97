/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, FlatList, Text, Pressable, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ForumPost from '../../Forum/components/ForumPost';
import Answers from '../Answers';
import type { ForumAnswerProps } from '../Answers/types';
import type { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';
import type { ForumPostProps } from '../../Forum/components/ForumPost/types';
import { useAnswers } from './useHook';
import { PreviewIconType } from '../../Forum/components/ForumPost/types';

function mapQuestionToForumPostProps(question: ForumQuestionDetailWithAnswersI): ForumPostProps {
  const baseDate = question.liveStartedAt
    ? new Date(question.liveStartedAt)
    : new Date(question.createdAt);
  const localBaseTime = new Date(
    baseDate.getFullYear(),
    baseDate.getMonth(),
    baseDate.getDate(),
    baseDate.getHours(),
    baseDate.getMinutes(),
    baseDate.getSeconds()
  ).getTime();
  const endTime = localBaseTime + 24 * 60 * 60 * 1000;

  const media = question.media ?? [];
  const extensionToIcon: Record<string, PreviewIconType> = {
    jpg: 'photo',
    jpeg: 'photo',
    webp: 'photo',
    pdf: 'pdf',
    xls: 'excel',
    xlsx: 'excel',
  };
  const previewIcons: PreviewIconType[] = media
    .map((m) => extensionToIcon[m.fileExtension])
    .filter(Boolean);

  return {
    postId: question.id,
    communityId: '',
    profile: question.profile,
    type: question.type === 'TROUBLESHOOT' ? 'troubleshooting' : 'question',
    topics: question.topics?.map(t => ({ id: t.id, label: t.name })) ?? [],
    equipment: [
      ...(question.equipmentCategory
        ? [{ id: question.equipmentCategory.id, label: question.equipmentCategory.name }]
        : []),
      ...(question.equipmentManufacturer
        ? [{ id: question.equipmentManufacturer.id, label: question.equipmentManufacturer.name }]
        : []),
      ...(question.equipmentModel
        ? [{ id: question.equipmentModel.id, label: question.equipmentModel.name }]
        : []),
    ],
    title: question.title,
    isSolved: question.isSolved,
    description: question.description ?? '',
    media: question.media ?? [],
    previewIcons,
    upVotes: question.upvoteCount,
    downVotes: question.downvoteCount,
    answers: question.answerCount,
    comments: 0,
    endTime,
    answerView: true,
  };
}

const AnswersList = ({
  answers,
  question,
}: {
  answers: ForumAnswerProps[];
  question: ForumQuestionDetailWithAnswersI;
}) => {
  const { handleLoadMore, loading, nextCursorId } = useAnswers();

  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const postProps = mapQuestionToForumPostProps(question);

  return (
    <View className="px-2 flex-1">
      <FlatList
        data={answers}
        keyExtractor={(item) => item.answerId}
        renderItem={({ item }) => (
          <Pressable onPress={() => navigation.navigate('ForumComments', { answerId: item.answerId })}>
            <Answers answer={item} question={question}/>
          </Pressable>
        )}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
          paddingVertical: 20,
        }}
        ListHeaderComponent={
          <View className="bg-white">
            <ForumPost
              post={postProps}
            />
            <Text className="text-base font-medium text-black px-5 py-1">
              {question.answers?.length} answers
            </Text>
          </View>
        }
        onEndReached={() => {
          if (nextCursorId) {
            handleLoadMore(question.id);
          }
        }}
        onEndReachedThreshold={0.8}
        ListFooterComponent={
          loading && nextCursorId ? (
            <View className="py-4 items-center">
              <ActivityIndicator size="small" color="#448600" />
            </View>
          ) : null
        }
      />
    </View>
  );
};

export default AnswersList;
