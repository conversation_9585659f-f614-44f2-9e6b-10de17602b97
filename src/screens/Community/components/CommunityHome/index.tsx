import React from 'react';
import { View, FlatList, Pressable } from 'react-native';
import ForumPost from '@/src/screens/Forum/components/ForumPost';
import { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';
import CommunityHomeHead from '../CommunityHomeHead';
import { CommunityHomePropsI } from './types';
import useForumPostList from '@/src/screens/Forum/components/ForumPostList/useHook';

const sampleForumPosts: ForumPostProps[] = [
  {
    postId: '1',
    profileId: 'user123',
    communityId: 'community123',
    type: 'question',
    topics: [
      { id: '1', label: 'Topic name' },
      { id: '2', label: 'Topic name' },
      { id: '3', label: 'Topic name' },
    ],
    title: 'Lorem Ipsum',
    isSolved: true,
    description: 'Lorem ipsum dolor sit amet, consectetur adi...',
    previewIcons: ['pdf', 'photo', 'video', 'excel', 'link'],
    upVotes: 16,
    downVotes: 13,
    answers: 5,
    comments: 8,
    endTime: Date.now() + 1000 * 60 * 60 * 24 * 7,
    answerView: false,
    community: 'Community Name',
  },
  {
    postId: '2',
    profileId: 'user456',
    communityId: 'community123',
    type: 'question',
    topics: [
      { id: '4', label: 'Topic name' },
      { id: '5', label: 'Topic name' },
      { id: '6', label: 'Topic name' },
    ],
    title: 'Lorem Ipsum',
    isSolved: true,
    description: 'Lorem ipsum dolor sit amet, consectetur adi...',
    previewIcons: ['pdf', 'photo', 'video', 'excel', 'link'],
    upVotes: 16,
    downVotes: 13,
    answers: 3,
    comments: 12,
    endTime: Date.now() + 1000 * 60 * 60 * 24 * 7,
    answerView: false,
    community: 'Community Name'
  }]

const CommunityHome: React.FC<CommunityHomePropsI> = ({ posts = sampleForumPosts }) => {
  const { handleSelectPost } = useForumPostList();

  const renderForumPost = ({ item }: { item: ForumPostProps }) => (
    <Pressable onPress={() => handleSelectPost(item)}>
      <ForumPost post={item} />
    </Pressable>
  );

  return (
    <View className="flex-1 bg-gray-50">
      <CommunityHomeHead communityName="Community Name" memberCount={12} role="admin" />
      <FlatList
        data={posts}
        renderItem={renderForumPost}
        keyExtractor={(item) => item.postId}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: 20,
        }}
      />
    </View>
  );
};

export default CommunityHome;
