import { apiCall } from '@/src/services/api';
import type {
  CreateQuestionPayloadI,
  QuestionCreateResponseI,
  QuestionI,
  ForumQuestionFetchManyPayloadI,
  ForumQuestionFetchManyResultI,
  ForumQuestionDetailWithAnswersI,
} from './types';

export const createQuestionAPI = async (
  payload: CreateQuestionPayloadI,
): Promise<QuestionCreateResponseI> => {
  const result = await apiCall<CreateQuestionPayloadI, QuestionCreateResponseI>(
    '/backend/api/v1/forum/question',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchQuestionAPI = async (id: string): Promise<QuestionI> => {
  const result = await apiCall<{ id: string }, QuestionI>(
    `/backend/api/v1/community/question/${id}`,
    'GET',
    {
      isAuth: true,
      query: { id },
    },
  );

  return result;
};

export const editQuestionAPI = async (
  payload: CreateQuestionPayloadI,
  questionId: string,
): Promise<QuestionCreateResponseI> => {
  const result = await apiCall<CreateQuestionPayloadI, QuestionCreateResponseI>(
    `/backend/api/v1/community/question/${questionId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchForumQuestionsAPI = async (
  query: ForumQuestionFetchManyPayloadI,
): Promise<ForumQuestionFetchManyResultI> => {
  const result = await apiCall<unknown, ForumQuestionFetchManyResultI>(
    '/backend/api/v1/forum/questions',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const fetchForumQuestionForClientAPI = async (
  questionId: string,
): Promise<ForumQuestionDetailWithAnswersI> => {
  return apiCall<{ id: string }, ForumQuestionDetailWithAnswersI>(
    `/backend/api/v1/forum/question/${questionId}`,
    'GET',
    {
      isAuth: true,
      query: { id: questionId },
    },
  );

  // Dummy data
  // return {
  //   id: questionId,
  //   cursorId: 1,
  //   title: 'Dummy Forum Question Title',
  //   description: 'This is a dummy description for the forum question.',
  //   type: 'NORMAL',
  //   upvoteCount: 10,
  //   downvoteCount: 2,
  //   answerCount: 1,
  //   isLive: false,
  //   liveStartedAt: null,
  //   shipImo: null,
  //   isSolved: false,
  //   equipmentCategory: {
  //     id: 'cat1',
  //     name: 'Engine',
  //     dataType: 'master',
  //   },
  //   equipmentModel: {
  //     id: 'model1',
  //     name: 'Model X',
  //     dataType: 'master',
  //   },
  //   equipmentManufacturer: {
  //     id: 'man1',
  //     name: 'Manufacturer Y',
  //     dataType: 'master',
  //   },
  //   createdAt: new Date().toISOString(),
  //   isEdited: false,
  //   profile: {
  //     id: 'user1',
  //     name: 'John Doe',
  //     avatar: null,
  //   },
  //   topics: [
  //     { id: 't1', name: 'Maintenance', dataType: 'master' },
  //     { id: 't2', name: 'Safety', dataType: 'master' },
  //   ],
  //   departments: [{ id: 'd1', name: 'Engineering', dataType: 'master' }],
  //   media: [
  //     {
  //       id: 'm1',
  //       fileUrl: 'https://example.com/file.jpg',
  //       fileExtension: 'jpg',
  //     },
  //   ],
  //   answers: [
  //     {
  //       id: 'a1',
  //       cursorId: 1,
  //       text: 'This is a dummy .',
  //       upvoteCount: 5,
  //       downvoteCount: 0,
  //       commentCount: 2,
  //       status: 'VERIFIED',
  //       isEdited: false,
  //       createdAt: new Date().toISOString(),
  //       profile: {
  //         id: 'user2',
  //         name: 'Jane Smith',
  //         avatar: null,
  //       },
  //     },
  //   ],
  // };
};
