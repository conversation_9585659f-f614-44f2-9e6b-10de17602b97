/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

export type VoteTypeE = 'UPVOTE' | 'DOWNVOTE';

export type ForumQuestionVoteI = {
  id: string;
  cursorId: number;
};

export type ForumQuestionVoteFetchManyI = {
  questionId: string;
  type: VoteTypeE;
  cursorId?: number | null;
  pageSize?: number;
};

export type ForumQuestionVoteCreateOneI = {
  questionId: string;
  type: VoteTypeE;
};

export type ForumQuestionVoteDeleteOneI = {
  questionId: string;
};

export type ProfileExternalI = {
  id: string;
  avatar: string | null;
  name: string;
  designationText: string | null;
  cursorId: number;
};

export type TotalCursorDataI<T> = {
  total: number;
  data: T[];
  nextCursorId: number | null;
};

export type ForumQuestionVoteFetchManyResultI = TotalCursorDataI<ProfileExternalI>;

// Answer vote types (similar structure for answers)
export type ForumAnswerVoteI = {
  id: string;
  cursorId: number;
};

export type ForumAnswerVoteFetchManyI = {
  answerId: string;
  type: VoteTypeE;
  cursorId?: number | null;
  pageSize?: number;
};

export type ForumAnswerVoteCreateOneI = {
  answerId: string;
  type: VoteTypeE;
};

export type ForumAnswerVoteDeleteOneI = {
  answerId: string;
};

export type ForumAnswerVoteFetchManyResultI = TotalCursorDataI<ProfileExternalI>;

// Question Comment types
export type QuestionCommentProfileI = {
  id: string;
  name: string;
  avatar: string | null;
};

export type QuestionCommentReplyI = {
  id: string;
  cursorId: number;
  text: string;
  isAnonymous: boolean;
  createdAt: string;
  profile: QuestionCommentProfileI | null;
};

export type QuestionCommentI = {
  id: string;
  cursorId: number;
  text: string;
  isAnonymous: boolean;
  replyCount: number;
  createdAt: string;
  profile: QuestionCommentProfileI | null;
  replies: QuestionCommentReplyI[] | null;
};

export type QuestionCommentCreateOneI = {
  questionId: string;
  text: string;
  parentCommentId?: string;
};

export type QuestionCommentCreateOneResponseI = {
  id: string;
  cursorId: number;
};

export type QuestionCommentFetchManyI = {
  questionId: string;
  cursorId?: number | null;
  pageSize?: number;
};

export type QuestionCommentFetchManyResultI = TotalCursorDataI<QuestionCommentI>;

export type QuestionCommentFetchRepliesI = {
  questionId: string;
  parentCommentId: string;
  cursorId?: number | null;
  pageSize?: number;
};

export type QuestionCommentFetchRepliesResultI = TotalCursorDataI<QuestionCommentReplyI>;

export type QuestionCommentDeleteOneI = {
  commentId: string;
};
