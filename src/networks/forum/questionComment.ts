/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import type {
  QuestionCommentCreateOneI,
  QuestionCommentCreateOneResponseI,
  QuestionCommentFetchManyI,
  QuestionCommentFetchManyResultI,
  QuestionCommentFetchRepliesI,
  QuestionCommentFetchRepliesResultI,
  QuestionCommentDeleteOneI,
} from './types';

export const createQuestionCommentAPI = async (
  payload: QuestionCommentCreateOneI,
): Promise<QuestionCommentCreateOneResponseI> => {
  const result = await apiCall<QuestionCommentCreateOneI, QuestionCommentCreateOneResponseI>(
    '/backend/api/v1/forum/question-comment',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
  return result;
};

export const fetchQuestionCommentsAPI = async (
  query: QuestionCommentFetchManyI,
): Promise<QuestionCommentFetchManyResultI> => {
  const result = await apiCall<unknown, QuestionCommentFetchManyResultI>(
    '/backend/api/v1/forum/question-comments',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
  return result;
};

export const fetchQuestionCommentRepliesAPI = async (
  query: QuestionCommentFetchRepliesI,
): Promise<QuestionCommentFetchRepliesResultI> => {
  const result = await apiCall<unknown, QuestionCommentFetchRepliesResultI>(
    '/backend/api/v1/forum/question-comment-replies',
    'GET',
    {
      isAuth: true,
      query,
    },
  );
  return result;
};

export const deleteQuestionCommentAPI = async (
  query: QuestionCommentDeleteOneI,
): Promise<void> => {
  await apiCall<unknown, void>(
    '/backend/api/v1/forum/question-comment',
    'DELETE',
    {
      isAuth: true,
      query,
    },
  );
};
