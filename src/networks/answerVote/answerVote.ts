import { apiCall } from '@/src/services/api';
import type {
  ForumAnswerVoteFetchManyParamsI,
  ForumAnswerVoteFetchManyResponseI,
  ForumAnswerVoteCreateOnePayloadI,
  ForumAnswerVoteCreateOneResponseI,
} from './types';

export const fetchForumAnswerVotesAPI = async (
  params: ForumAnswerVoteFetchManyParamsI
): Promise<ForumAnswerVoteFetchManyResponseI> => {
    const query = {
    answerId: params.answerId,
    ...(params.cursorId !== undefined && { cursorId: params.cursorId }),
    ...(params.pageSize !== undefined && { pageSize: params.pageSize }),
    type: params.type,
  };
  return apiCall<ForumAnswerVoteFetchManyParamsI, ForumAnswerVoteFetchManyResponseI>(
    '/backend/api/v1/forum/answer-votes',
    'GET',
    { isAuth: true, query }
  );
};

export const createForumAnswerVoteAPI = async (
  payload: ForumAnswerVoteCreateOnePayloadI
): Promise<ForumAnswerVoteCreateOneResponseI> => {
  return apiCall<ForumAnswerVoteCreateOnePayloadI, ForumAnswerVoteCreateOneResponseI>(
    '/backend/api/v1/forum/answer-vote',
    'POST',
    { isAuth: true, payload }
  );
};