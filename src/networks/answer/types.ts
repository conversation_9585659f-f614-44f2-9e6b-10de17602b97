import type { ProfileForDataI } from '@/src/networks/question/types';

export interface ForumAnswerWithProfileI {
  id: string;
  cursorId: number;
  text: string;
  isTextTruncated: boolean;
  upvoteCount: number;
  downvoteCount: number;
  commentCount: number;
  profile: ProfileForDataI;
}

export interface ForumAnswerDetailI {
  id: string;
  cursorId: number;
  text: string;
  upvoteCount: number;
  downvoteCount: number;
  commentCount: number;
  questionId: string;
  communityId: string;
  profileId: string;
  status: string;
  isEdited: boolean;
  createdAt: string;
  profile: ProfileForDataI;
}

export interface ForumAnswerCreateOnePayloadI {
  text: string;
  questionId: string;
}

export interface ForumAnswerCreateOneResponseI {
  id: string;
  cursorId: number;
}