import { apiCall } from '@/src/services/api';
import type {
  ForumAnswerWithProfileI,
  ForumAnswerDetailI,
  ForumAnswerCreateOnePayloadI,
  ForumAnswerCreateOneResponseI,
} from './types';

export const fetchForumAnswersAPI = async (
  questionId: string,
  cursorId?: number | null,
  pageSize: number = 10,
) => {
    return apiCall<
      { questionId: string; cursorId?: number; pageSize: number },
      { data: ForumAnswerWithProfileI[]; total: number; nextCursorId: number | null }
    >('/backend/api/v1/forum/answers', 'GET', {
      isAuth: true,
      query: { questionId, cursorId, pageSize },
    });
  // return {
  //   data: [
  //     {
  //       id: 'cd63b90c-4f99-42c7-993e-da801204e598',
  //       cursorId: 1,
  //       text: 'This is a dummy answer.',
  //       isTextTruncated: false,
  //       upvoteCount: 5,
  //       downvoteCount: 0,
  //       commentCount: 2,
  //       profile: {
  //         id: 'user2',
  //         name: '<PERSON>',
  //         avatar: null,
  //       },
  //     },
  //     {
  //       id: 'a2',
  //       cursorId: 2,
  //       text: 'Another dummy answer for testing.',
  //       isTextTruncated: false,
  //       upvoteCount: 2,
  //       downvoteCount: 1,
  //       commentCount: 1,
  //       profile: {
  //         id: 'user3',
  //         name: 'Alex Doe',
  //         avatar: null,
  //       },
  //     },
  //     {
  //       id: 'a3',
  //       cursorId: 3,
  //       text: 'Another dummy answer for testing.',
  //       isTextTruncated: false,
  //       upvoteCount: 2,
  //       downvoteCount: 1,
  //       commentCount: 1,
  //       profile: {
  //         id: 'user3',
  //         name: 'Alex Doe',
  //         avatar: null,
  //       },
  //     }
  //   ],
  //   total: 3,
  //   nextCursorId: null,
  // };
};

export const fetchForumAnswerDetailAPI = async (id: string) => {
    return apiCall<{ id: string }, ForumAnswerDetailI>('/backend/api/v1/forum/answer/text', 'GET', {
      isAuth: true,
      query: { id },
    });
  // return {
  //   id,
  //   cursorId: 1,
  //   text: 'This is a dummy answer.',
  //   upvoteCount: 5,
  //   downvoteCount: 0,
  //   commentCount: 2,
  //   questionId: 'dummy-question-id',
  //   communityId: 'dummy-community-id',
  //   profileId: 'user2',
  //   status: 'VERIFIED',
  //   isEdited: false,
  //   createdAt: new Date().toISOString(),
  //   profile: {
  //     id: 'user2',
  //     name: 'Jane Smith',
  //     avatar: null,
  //   },
  // };
};

export const createForumAnswerAPI = async (
  payload: ForumAnswerCreateOnePayloadI,
): Promise<ForumAnswerCreateOneResponseI> => {
  // Uncomment for real API:
  return apiCall<ForumAnswerCreateOnePayloadI, ForumAnswerCreateOneResponseI>(
    '/backend/api/v1/forum/answer',
    'POST',
    {
      isAuth: true,
      payload: {
        text: payload.text,
        questionId: payload.questionId,
      },
    },
  );

  // Dummy response for testing
  //   return {
  //     id: 'dummy-answer-id',
  //     cursorId: Math.floor(Math.random() * 1000) + 1,
  //   };
};
