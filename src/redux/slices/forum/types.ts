import type { ForumAnswerWithProfileI, ForumAnswerDetailI } from '@/src/networks/answer/types';
import type { ProfileExternalI } from '@/src/networks/answerVote/types';
import type { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';
import type { QuestionCommentI, QuestionCommentReplyI } from '@/src/networks/forum/types';

export type ForumState = {
  question: ForumQuestionDetailWithAnswersI | null;
  loading: boolean;
  error: string | null;
  answers: ForumAnswerWithProfileI[];
  total: number;
  nextCursorId: number | null;
  answerLoading: boolean;
  answerError: string | null;
  answerDetail: ForumAnswerDetailI | null;
  answerVotes: ProfileExternalI[];
  answerVotesTotal: number;
  answerVotesNextCursorId: number | null;
  answerVoteLoading: boolean;
  answerVoteError: string | null;
  // Question Comments
  questionComments: QuestionCommentI[];
  questionCommentsTotal: number;
  questionCommentsNextCursorId: number | null;
  questionCommentsLoading: boolean;
  questionCommentsError: string | null;
  // Comment Replies (keyed by parent comment ID)
  commentReplies: Record<string, {
    replies: QuestionCommentReplyI[];
    total: number;
    nextCursorId: number | null;
  }>;
  commentRepliesLoading: Record<string, boolean>;
  commentRepliesError: Record<string, string | null>;
};
