import type { ForumAnswerWithProfileI, ForumAnswerDetailI } from '@/src/networks/answer/types';
import type { ProfileExternalI } from '@/src/networks/answerVote/types';
import type { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';

export type ForumState = {
  question: ForumQuestionDetailWithAnswersI | null;
  loading: boolean;
  error: string | null;
  answers: ForumAnswerWithProfileI[];
  total: number;
  nextCursorId: number | null;
  answerLoading: boolean;
  answerError: string | null;
  answerDetail: ForumAnswerDetailI | null;
  answerVotes: ProfileExternalI[];
  answerVotesTotal: number;
  answerVotesNextCursorId: number | null;
  answerVoteLoading: boolean;
  answerVoteError: string | null;
};
