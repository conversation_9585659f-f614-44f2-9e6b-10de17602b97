import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  fetchForumAnswersAPI,
  fetchForumAnswerDetailAPI,
  createForumAnswerAPI,
} from '@/src/networks/answer/answer';
import type {
  ForumAnswerWithProfileI,
  ForumAnswerDetailI,
  ForumAnswerCreateOnePayloadI,
  ForumAnswerCreateOneResponseI,
} from '@/src/networks/answer/types';
import {
  fetchForumAnswerVotesAPI,
  createForumAnswerVoteAPI,
} from '@/src/networks/answerVote/answerVote';
import type {
  ForumAnswerVoteFetchManyParamsI,
  ForumAnswerVoteFetchManyResponseI,
  ForumAnswerVoteCreateOnePayloadI,
  ForumAnswerVoteCreateOneResponseI,
  ProfileExternalI,
} from '@/src/networks/answerVote/types';
import { fetchForumQuestionForClientAPI } from '@/src/networks/question/question';
import type { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';
import type { ForumState } from './types';

export const fetchForumQuestionDetail = createAsyncThunk<
  ForumQuestionDetailWithAnswersI,
  { questionId: string },
  { rejectValue: string }
>('forum/fetchForumQuestionDetail', async ({ questionId }, { rejectWithValue }) => {
  try {
    return await fetchForumQuestionForClientAPI(questionId);
  } catch (error) {
    if (error instanceof Error) {
      return rejectWithValue(error.message);
    }
    return rejectWithValue('Failed to fetch forum question');
  }
});

export const fetchAnswersForQuestion = createAsyncThunk(
  'answer/fetchAnswersForQuestion',
  async (
    {
      questionId,
      cursorId,
      pageSize,
    }: { questionId: string; cursorId?: number | null; pageSize?: number },
    { rejectWithValue },
  ) => {
    try {
      return await fetchForumAnswersAPI(questionId, cursorId, pageSize ?? 10);
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('Failed to fetch answers');
    }
  },
);

export const fetchAnswerDetail = createAsyncThunk(
  'answer/fetchAnswerDetail',
  async (id: string, { rejectWithValue }) => {
    try {
      return await fetchForumAnswerDetailAPI(id);
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('Failed to fetch answer detail');
    }
  },
);

export const createAnswer = createAsyncThunk<
  ForumAnswerCreateOneResponseI,
  ForumAnswerCreateOnePayloadI,
  { rejectValue: string }
>('answer/createAnswer', async (payload, { rejectWithValue }) => {
  try {
    return await createForumAnswerAPI(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to create answer');
  }
});

export const fetchAnswerVotes = createAsyncThunk<
  ForumAnswerVoteFetchManyResponseI,
  ForumAnswerVoteFetchManyParamsI,
  { rejectValue: string }
>('answerVote/fetchMany', async (params, { rejectWithValue }) => {
  try {
    return await fetchForumAnswerVotesAPI(params);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to fetch answer votes');
  }
});

export const createAnswerVote = createAsyncThunk<
  ForumAnswerVoteCreateOneResponseI,
  ForumAnswerVoteCreateOnePayloadI,
  { rejectValue: string }
>('answerVote/createOne', async (payload, { rejectWithValue }) => {
  try {
    return await createForumAnswerVoteAPI(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to vote on answer');
  }
});

const initialState: ForumState = {
  question: null,
  loading: false,
  error: null,
  answers: [],
  total: 0,
  nextCursorId: null,
  answerLoading: false,
  answerError: null,
  answerDetail: null,
  answerVotes: [] as ProfileExternalI[],
  answerVotesTotal: 0,
  answerVotesNextCursorId: null,
  answerVoteLoading: false,
  answerVoteError: null,
};

const forumSlice = createSlice({
  name: 'forum',
  initialState,
  reducers: {
    resetForumState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchForumQuestionDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchForumQuestionDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.question = action.payload;
      })
      .addCase(fetchForumQuestionDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch forum question';
      })
      .addCase(fetchAnswersForQuestion.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(fetchAnswersForQuestion.fulfilled, (state, action) => {
        state.answerLoading = false;
        if (action.meta.arg.cursorId) {
          state.answers = [...state.answers, ...action.payload.data];
        } else {
          state.answers = action.payload.data;
        }
        state.total = action.payload.total;
        state.nextCursorId = action.payload.nextCursorId;
      })
      .addCase(fetchAnswersForQuestion.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      })
      .addCase(fetchAnswerDetail.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(fetchAnswerDetail.fulfilled, (state, action) => {
        state.answerLoading = false;
        state.answerDetail = action.payload;
      })
      .addCase(fetchAnswerDetail.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      })
      .addCase(createAnswer.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(createAnswer.fulfilled, (state, action) => {
        state.answerLoading = false;
      })
      .addCase(createAnswer.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      })
      .addCase(fetchAnswerVotes.pending, (state) => {
        state.answerVoteLoading = true;
        state.answerVoteError = null;
      })
      .addCase(fetchAnswerVotes.fulfilled, (state, action) => {
        state.answerVoteLoading = false;
        state.answerVotes = action.payload.data;
        state.answerVotesTotal = action.payload.total;
        state.answerVotesNextCursorId = action.payload.nextCursorId;
      })
      .addCase(fetchAnswerVotes.rejected, (state, action) => {
        state.answerVoteLoading = false;
        state.answerVoteError = action.payload as string;
      })
      .addCase(createAnswerVote.pending, (state) => {
        state.answerVoteLoading = true;
        state.answerVoteError = null;
      })
      .addCase(createAnswerVote.fulfilled, (state) => {
        state.answerVoteLoading = false;
      })
      .addCase(createAnswerVote.rejected, (state, action) => {
        state.answerVoteLoading = false;
        state.answerVoteError = action.payload as string;
      });
  },
});

export const { resetForumState } = forumSlice.actions;
export default forumSlice.reducer;
