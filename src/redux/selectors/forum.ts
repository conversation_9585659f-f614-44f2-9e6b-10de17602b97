import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectForumState = (state: RootState) => state.forum;

export const selectForumQuestion = createSelector(
  [selectForumState],
  (forumState) => forumState.question,
);

export const selectForumLoading = createSelector(
  [selectForumState],
  (forumState) => forumState.loading,
);

export const selectForumError = createSelector(
  [selectForumState],
  (forumState) => forumState.error,
);

export const selectForumAnswers = createSelector(
  [selectForumState],
  (forumState) => forumState.answers,
);

export const selectForumAnswersTotal = createSelector(
  [selectForumState],
  (forumState) => forumState.total,
);

export const selectForumAnswersNextCursorId = createSelector(
  [selectForumState],
  (forumState) => forumState.nextCursorId,
);

export const selectForumAnswerLoading = createSelector(
  [selectForumState],
  (forumState) => forumState.answerLoading,
);

export const selectForumAnswerError = createSelector(
  [selectForumState],
  (forumState) => forumState.answerError,
);

export const selectForumAnswerDetail = createSelector(
  [selectForumState],
  (forumState) => forumState.answerDetail,
);

export const selectAnswerVotes = (state: RootState) => state.forum.answerVotes;
export const selectAnswerVotesTotal = (state: RootState) => state.forum.answerVotesTotal;
export const selectAnswerVotesNextCursorId = (state: RootState) =>
  state.forum.answerVotesNextCursorId;
export const selectAnswerVoteLoading = (state: RootState) => state.forum.answerVoteLoading;
export const selectAnswerVoteError = (state: RootState) => state.forum.answerVoteError;
